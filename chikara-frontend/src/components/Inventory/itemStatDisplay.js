const addUpgradeModifiers = (value, upgradeLevel) => {
    const multiplierThreshold = 3.0;

    const modifyValue = (val) => {
        const upgradeLevelModifier = 1 + upgradeLevel * 0.05;
        return parseFloat((val * upgradeLevelModifier).toFixed(0));
    };

    const modifyMultiplier = (multiplier) => {
        const basePercentage = multiplier - 1;
        const upgradeLevelModifier = upgradeLevel * 0.05;
        const newPercentage = basePercentage + basePercentage * upgradeLevelModifier;
        return parseFloat((1 + newPercentage).toFixed(2));
    };

    if (upgradeLevel && upgradeLevel > 0) {
        return value < multiplierThreshold ? modifyMultiplier(value) : modifyValue(value);
    }
    return value;
};

const formatStat = (value, label, upgradeLevel, hidePlus, showDiff, isModifier) => {
    const modifiedValue = Math.round(addUpgradeModifiers(value, upgradeLevel));
    if (showDiff) {
        const prevValue = Math.round(addUpgradeModifiers(value, upgradeLevel - 1));
        const diff = parseFloat(Math.abs(modifiedValue - prevValue).toFixed(2));

        if (!diff || diff <= 0) return null;
        return `+${diff}${isModifier ? "%" : ""}`;
    }
    return `${modifiedValue > 0 && !hidePlus ? "+" : ""}${modifiedValue} ${label}`;
};

const addStatIfValid = (arr, value, upgradeLevel, label, hidePlus, showDiff, isModifier) => {
    if (value && value !== 0) {
        arr.push(formatStat(value, label, upgradeLevel, hidePlus, showDiff, isModifier));
    }
};

export const itemStatDisplay = (item, metabolismTalent, upgradeLevel, hidePlus = false, showDiff = false) => {
    if (!item) return [];
    const arr = [];
    const { itemType, armour, statModifiers = {}, energy, actionPoints, health, damage } = item;

    const parsedModifiers = typeof statModifiers === "string" ? JSON.parse(statModifiers) : statModifiers;

    const statLabels = {
        defence: "% DEF",
        strength: "% STR",
        dexterity: "% DEX",
        endurance: "% END",
        lifesteal: "% LIFE STEAL",
        health: " HP",
    };

    // Handle weapon stats
    if (["weapon", "ranged", "offhand"].includes(itemType)) {
        addStatIfValid(arr, damage, upgradeLevel, "DMG", hidePlus, showDiff);
    }

    // Handle armour stats
    addStatIfValid(arr, armour, upgradeLevel, "ARMOR", hidePlus, showDiff);

    // Handle stat modifiers
    if (parsedModifiers) {
        for (const [key, label] of Object.entries(statLabels)) {
            if (key in parsedModifiers) {
                const value =
                    key === "health" && itemType === "finger" ? parsedModifiers[key] : (parsedModifiers[key] - 1) * 100;
                addStatIfValid(arr, value, upgradeLevel, label, hidePlus, showDiff, true);
            }
        }
    }

    // Handle consumable stats
    if (itemType === "consumable") {
        addStatIfValid(arr, energy, null, "ENERGY", hidePlus);
        addStatIfValid(arr, actionPoints, null, "AP", hidePlus);

        if (health && health !== 0) {
            const baseHealth = metabolismTalent ? Math.round(health * metabolismTalent.modifier) : health;
            addStatIfValid(arr, baseHealth, null, "HP", hidePlus);
        }
    }
    return arr;
};
