import { cn } from "@/lib/utils";
import { BarChart3, PenLine } from "lucide-react";
import React from "react";

interface IconButtonProps {
    icon: string;
    onClick?: () => void;
    className?: string;
    iconClassName?: string;
}

function IconButton({ icon, onClick, className, iconClassName }: IconButtonProps) {
    const displayIcon = () => {
        if (icon === "edit") {
            return <PenLine className={cn(iconClassName ? iconClassName : "size-8 fill-white text-white")} />;
        } else if (icon === "poll") {
            return <BarChart3 className={cn(iconClassName ? iconClassName : "size-8 fill-white text-white")} />;
        } else {
            return (
                <img src={icon} alt="" className={cn(iconClassName ? iconClassName : "size-8 fill-white text-white")} />
            );
        }
    };
    return (
        <button
            type="button"
            className={cn(
                "primaryBtn flex items-center justify-center rounded-xl font-display font-semibold",
                className
            )}
            onClick={onClick}
        >
            {displayIcon()}
        </button>
    );
}

export default IconButton;
